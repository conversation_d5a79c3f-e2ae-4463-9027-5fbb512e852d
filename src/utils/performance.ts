/**
 * Performance monitoring and metrics collection utilities
 */

/**
 * Performance metric data
 */
export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

/**
 * Performance statistics
 */
export interface PerformanceStats {
  totalCalls: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  totalDuration: number;
  recentMetrics: PerformanceMetric[];
}

/**
 * Performance monitor for tracking operation performance
 */
export class PerformanceMonitor {
  private metrics = new Map<string, PerformanceMetric[]>();
  private readonly maxMetricsPerOperation: number;

  constructor(maxMetricsPerOperation = 100) {
    this.maxMetricsPerOperation = maxMetricsPerOperation;
  }

  /**
   * Start timing an operation
   */
  startTimer(operationName: string): PerformanceTimer {
    return new PerformanceTimer(operationName, this);
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const operationMetrics = this.metrics.get(metric.name)!;
    operationMetrics.push(metric);

    // Keep only the most recent metrics
    if (operationMetrics.length > this.maxMetricsPerOperation) {
      operationMetrics.shift();
    }
  }

  /**
   * Get statistics for an operation
   */
  getStats(operationName: string): PerformanceStats | null {
    const metrics = this.metrics.get(operationName);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const durations = metrics.map(m => m.duration);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);

    return {
      totalCalls: metrics.length,
      averageDuration: totalDuration / metrics.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalDuration,
      recentMetrics: [...metrics].slice(-10) // Last 10 metrics
    };
  }

  /**
   * Get all operation names being monitored
   */
  getOperationNames(): string[] {
    return Array.from(this.metrics.keys());
  }

  /**
   * Get a summary of all operations
   */
  getAllStats(): Record<string, PerformanceStats> {
    const stats: Record<string, PerformanceStats> = {};
    
    for (const operationName of this.getOperationNames()) {
      const operationStats = this.getStats(operationName);
      if (operationStats) {
        stats[operationName] = operationStats;
      }
    }

    return stats;
  }

  /**
   * Clear metrics for an operation
   */
  clearMetrics(operationName: string): void {
    this.metrics.delete(operationName);
  }

  /**
   * Clear all metrics
   */
  clearAllMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Get slow operations (above threshold)
   */
  getSlowOperations(thresholdMs = 1000): Array<{ name: string; stats: PerformanceStats }> {
    const slowOps: Array<{ name: string; stats: PerformanceStats }> = [];

    for (const operationName of this.getOperationNames()) {
      const stats = this.getStats(operationName);
      if (stats && stats.averageDuration > thresholdMs) {
        slowOps.push({ name: operationName, stats });
      }
    }

    return slowOps.sort((a, b) => b.stats.averageDuration - a.stats.averageDuration);
  }
}

/**
 * Timer for measuring operation performance
 */
export class PerformanceTimer {
  private startTime: number;
  private endTime?: number;

  constructor(
    private operationName: string,
    private monitor: PerformanceMonitor,
    private metadata?: Record<string, unknown>
  ) {
    this.startTime = performance.now();
  }

  /**
   * Stop the timer and record the metric
   */
  stop(additionalMetadata?: Record<string, unknown>): number {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;

    const metric: PerformanceMetric = {
      name: this.operationName,
      duration,
      timestamp: new Date(),
      metadata: { ...this.metadata, ...additionalMetadata }
    };

    this.monitor.recordMetric(metric);
    return duration;
  }

  /**
   * Get the current elapsed time without stopping the timer
   */
  getElapsed(): number {
    return performance.now() - this.startTime;
  }
}

/**
 * Decorator for automatically timing method calls
 */
export function timed(operationName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const opName = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const timer = globalPerformanceMonitor.startTimer(opName);
      
      try {
        const result = await originalMethod.apply(this, args);
        timer.stop({ success: true });
        return result;
      } catch (error) {
        timer.stop({ success: false, error: error instanceof Error ? error.message : String(error) });
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Utility function to measure async operations
 */
export async function measureAsync<T>(
  operationName: string,
  operation: () => Promise<T>,
  metadata?: Record<string, unknown>
): Promise<{ result: T; duration: number }> {
  const timer = globalPerformanceMonitor.startTimer(operationName);
  timer.metadata = metadata;

  try {
    const result = await operation();
    const duration = timer.stop({ success: true });
    return { result, duration };
  } catch (error) {
    const duration = timer.stop({ 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    });
    throw error;
  }
}

/**
 * Utility function to measure sync operations
 */
export function measureSync<T>(
  operationName: string,
  operation: () => T,
  metadata?: Record<string, unknown>
): { result: T; duration: number } {
  const timer = globalPerformanceMonitor.startTimer(operationName);
  timer.metadata = metadata;

  try {
    const result = operation();
    const duration = timer.stop({ success: true });
    return { result, duration };
  } catch (error) {
    const duration = timer.stop({ 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    });
    throw error;
  }
}

/**
 * Global performance monitor instance
 */
export const globalPerformanceMonitor = new PerformanceMonitor();

/**
 * Performance reporting utilities
 */
export class PerformanceReporter {
  /**
   * Generate a performance report
   */
  static generateReport(monitor: PerformanceMonitor = globalPerformanceMonitor): string {
    const allStats = monitor.getAllStats();
    const operationNames = Object.keys(allStats).sort();

    if (operationNames.length === 0) {
      return 'No performance metrics available.';
    }

    let report = 'Performance Report\n';
    report += '==================\n\n';

    for (const operationName of operationNames) {
      const stats = allStats[operationName];
      report += `Operation: ${operationName}\n`;
      report += `  Total Calls: ${stats.totalCalls}\n`;
      report += `  Average Duration: ${stats.averageDuration.toFixed(2)}ms\n`;
      report += `  Min Duration: ${stats.minDuration.toFixed(2)}ms\n`;
      report += `  Max Duration: ${stats.maxDuration.toFixed(2)}ms\n`;
      report += `  Total Duration: ${stats.totalDuration.toFixed(2)}ms\n\n`;
    }

    // Add slow operations section
    const slowOps = monitor.getSlowOperations(1000);
    if (slowOps.length > 0) {
      report += 'Slow Operations (>1000ms average):\n';
      report += '===================================\n';
      for (const { name, stats } of slowOps) {
        report += `  ${name}: ${stats.averageDuration.toFixed(2)}ms average\n`;
      }
      report += '\n';
    }

    return report;
  }

  /**
   * Log performance warnings for slow operations
   */
  static logSlowOperations(
    monitor: PerformanceMonitor = globalPerformanceMonitor,
    thresholdMs = 5000
  ): void {
    const slowOps = monitor.getSlowOperations(thresholdMs);
    
    for (const { name, stats } of slowOps) {
      console.warn(
        `Slow operation detected: ${name} (${stats.averageDuration.toFixed(2)}ms average, ${stats.totalCalls} calls)`
      );
    }
  }
}
